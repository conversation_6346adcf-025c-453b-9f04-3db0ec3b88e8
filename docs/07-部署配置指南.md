# 宠物博客站群系统 - 部署配置指南

## 1. 部署环境概述

### 1.1 服务器信息
- **服务器类型**: Linux VPS
- **管理面板**: 宝塔面板
- **操作系统**: CentOS/Ubuntu
- **数据库**: MySQL 5.7.44
- **Web服务器**: Nginx
- **Node.js**: 18.x LTS

### 1.2 域名配置
```
主域名配置:
- petblog.com (英语)
- haustierblog.de (德语)  
- питомцыблог.ru (俄语)

子域名配置:
- api.petblog.com (API服务)
- admin.petblog.com (管理后台)
```

## 2. 服务器环境准备

### 2.1 宝塔面板安装
```bash
# CentOS安装命令
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh

# Ubuntu安装命令
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

### 2.2 必要软件安装
通过宝塔面板安装以下软件：
- Nginx 1.20+
- MySQL 5.7.44
- PHP 8.0+ (用于宝塔面板管理)
- Node.js 18.x
- PM2 (Node.js进程管理)
- Redis (可选，用于缓存)

### 2.3 Node.js环境配置
```bash
# 安装Node.js版本管理器
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc

# 安装Node.js 18.x LTS
nvm install 18
nvm use 18
nvm alias default 18

# 安装PM2
npm install -g pm2

# 安装pnpm (推荐的包管理器)
npm install -g pnpm
```

## 3. 数据库配置

### 3.1 MySQL数据库设置
```sql
-- 创建数据库
CREATE DATABASE bengtai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权
CREATE USER 'bengtai'@'%' IDENTIFIED BY 'weizhen258';
GRANT ALL PRIVILEGES ON bengtai.* TO 'bengtai'@'%';
FLUSH PRIVILEGES;

-- 优化MySQL配置 (在/etc/mysql/my.cnf中添加)
[mysqld]
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
max_connections = 200
query_cache_size = 32M
query_cache_type = 1
```

### 3.2 数据库初始化
```bash
# 导入数据库结构
mysql -u bengtai -p bengtai < database/schema.sql

# 导入初始数据
mysql -u bengtai -p bengtai < database/seeds.sql
```

## 4. 后端部署配置

### 4.1 项目部署目录结构
```
/www/wwwroot/pet-blog-system/
├── backend/                 # 后端应用
├── frontend/               # 前端构建文件
├── uploads/                # 上传文件目录
├── logs/                   # 日志目录
├── scripts/                # 部署脚本
└── ecosystem.config.js     # PM2配置文件
```

### 4.2 后端环境变量配置
创建 `backend/.env` 文件：
```env
# 应用配置
NODE_ENV=production
PORT=3000
APP_NAME=Pet Blog System

# 数据库配置
DB_HOST=************
DB_PORT=3306
DB_NAME=bengtai
DB_USER=bengtai
DB_PASSWORD=weizhen258

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# 翻译API配置
TRANSLATION_API_URL=https://ai.wanderintree.top
TRANSLATION_API_KEY=sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d
TRANSLATION_MODEL=gemini-2.5-pro

# 文件上传配置
UPLOAD_DIR=/www/wwwroot/pet-blog-system/uploads
MAX_FILE_SIZE=10485760
ALLOWED_IMAGE_TYPES=jpg,jpeg,png,webp,gif

# 邮件配置 (可选)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# 缓存配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=

# 日志配置
LOG_LEVEL=info
LOG_DIR=/www/wwwroot/pet-blog-system/logs
```

### 4.3 PM2进程管理配置
创建 `ecosystem.config.js` 文件：
```javascript
module.exports = {
  apps: [
    {
      name: 'pet-blog-api',
      script: './backend/dist/index.js',
      cwd: '/www/wwwroot/pet-blog-system',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      error_file: './logs/api-error.log',
      out_file: './logs/api-out.log',
      log_file: './logs/api-combined.log',
      time: true,
      max_memory_restart: '500M',
      node_args: '--max-old-space-size=512',
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'uploads'],
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s'
    }
  ]
};
```

## 5. 前端部署配置

### 5.1 Astro构建配置
修改 `frontend/astro.config.mjs`：
```javascript
import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';

export default defineConfig({
  integrations: [tailwind()],
  output: 'static',
  build: {
    assets: 'assets'
  },
  site: 'https://petblog.com',
  base: '/',
  trailingSlash: 'ignore',
  compressHTML: true,
  vite: {
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['astro']
          }
        }
      }
    }
  }
});
```

### 5.2 多语言站点构建脚本
创建 `scripts/build-sites.sh`：
```bash
#!/bin/bash

# 构建英语站点
cd frontend
echo "Building English site..."
LANGUAGE=en DOMAIN=petblog.com npm run build
mv dist ../dist-en

# 构建德语站点
echo "Building German site..."
LANGUAGE=de DOMAIN=haustierblog.de npm run build
mv dist ../dist-de

# 构建俄语站点
echo "Building Russian site..."
LANGUAGE=ru DOMAIN=питомцыблог.ru npm run build
mv dist ../dist-ru

echo "All sites built successfully!"
```

## 6. Nginx配置

### 6.1 主配置文件
创建 `/etc/nginx/sites-available/pet-blog-system`：
```nginx
# 英语站点配置
server {
    listen 80;
    listen 443 ssl http2;
    server_name petblog.com www.petblog.com;
    
    # SSL配置
    ssl_certificate /path/to/ssl/petblog.com.crt;
    ssl_certificate_key /path/to/ssl/petblog.com.key;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        root /www/wwwroot/pet-blog-system/dist-en;
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # 上传文件访问
    location /uploads/ {
        root /www/wwwroot/pet-blog-system;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # 主站点文件
    location / {
        root /www/wwwroot/pet-blog-system/dist-en;
        try_files $uri $uri/ $uri.html /404.html;
        
        # 安全配置
        location ~ /\. {
            deny all;
        }
    }
    
    # 特殊文件处理
    location = /robots.txt {
        root /www/wwwroot/pet-blog-system/dist-en;
        expires 1d;
    }
    
    location = /sitemap.xml {
        root /www/wwwroot/pet-blog-system/dist-en;
        expires 1d;
    }
    
    # HTTP重定向到HTTPS
    if ($scheme != "https") {
        return 301 https://$host$request_uri;
    }
}

# 德语站点配置
server {
    listen 80;
    listen 443 ssl http2;
    server_name haustierblog.de www.haustierblog.de;
    
    # SSL配置
    ssl_certificate /path/to/ssl/haustierblog.de.crt;
    ssl_certificate_key /path/to/ssl/haustierblog.de.key;
    
    # 其他配置与英语站点类似，但root指向dist-de
    root /www/wwwroot/pet-blog-system/dist-de;
    
    # ... (其他配置项)
}

# 俄语站点配置
server {
    listen 80;
    listen 443 ssl http2;
    server_name питомцыблог.ru www.питомцыблог.ru;
    
    # SSL配置
    ssl_certificate /path/to/ssl/питомцыблог.ru.crt;
    ssl_certificate_key /path/to/ssl/питомцыблог.ru.key;
    
    # 其他配置与英语站点类似，但root指向dist-ru
    root /www/wwwroot/pet-blog-system/dist-ru;
    
    # ... (其他配置项)
}
```

### 6.2 性能优化配置
在 `/etc/nginx/nginx.conf` 中添加：
```nginx
http {
    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # 缓冲区配置
    client_body_buffer_size 128k;
    client_max_body_size 10m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;
    
    # 连接限制
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    limit_req_zone $binary_remote_addr zone=req_limit_per_ip:10m rate=5r/s;
    
    # 在server块中使用
    limit_conn conn_limit_per_ip 20;
    limit_req zone=req_limit_per_ip burst=10 nodelay;
}
```

## 7. SSL证书配置

### 7.1 Let's Encrypt免费证书
```bash
# 安装Certbot
sudo apt-get update
sudo apt-get install certbot python3-certbot-nginx

# 为每个域名申请证书
sudo certbot --nginx -d petblog.com -d www.petblog.com
sudo certbot --nginx -d haustierblog.de -d www.haustierblog.de
sudo certbot --nginx -d питомцыблог.ru -d www.питомцыблог.ru

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 8. 部署脚本

### 8.1 自动部署脚本
创建 `scripts/deploy.sh`：
```bash
#!/bin/bash

set -e

echo "Starting deployment..."

# 拉取最新代码
git pull origin main

# 安装后端依赖
cd backend
pnpm install --production
pnpm run build

# 构建前端
cd ../frontend
pnpm install
pnpm run build:all

# 重启服务
pm2 restart pet-blog-api

# 重载Nginx
sudo nginx -t && sudo nginx -s reload

echo "Deployment completed successfully!"
```

### 8.2 备份脚本
创建 `scripts/backup.sh`：
```bash
#!/bin/bash

BACKUP_DIR="/backup/pet-blog-system"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u bengtai -pweizhen258 bengtai > $BACKUP_DIR/database_$DATE.sql

# 备份上传文件
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz uploads/

# 备份配置文件
tar -czf $BACKUP_DIR/config_$DATE.tar.gz backend/.env ecosystem.config.js

# 清理7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

## 9. 监控与维护

### 9.1 日志监控
```bash
# 查看API日志
pm2 logs pet-blog-api

# 查看Nginx访问日志
tail -f /var/log/nginx/access.log

# 查看Nginx错误日志
tail -f /var/log/nginx/error.log

# 查看MySQL慢查询日志
tail -f /var/log/mysql/mysql-slow.log
```

### 9.2 性能监控
```bash
# 系统资源监控
htop
iotop
nethogs

# 数据库性能监控
mysqladmin -u bengtai -p processlist
mysqladmin -u bengtai -p status

# Node.js应用监控
pm2 monit
```

## 10. 故障排除

### 10.1 常见问题解决
```bash
# PM2进程无法启动
pm2 delete all
pm2 start ecosystem.config.js

# Nginx配置错误
nginx -t
systemctl restart nginx

# 数据库连接问题
mysql -u bengtai -p -h ************

# 文件权限问题
chown -R www-data:www-data /www/wwwroot/pet-blog-system
chmod -R 755 /www/wwwroot/pet-blog-system
```

### 10.2 应急恢复
```bash
# 恢复数据库
mysql -u bengtai -p bengtai < /backup/pet-blog-system/database_latest.sql

# 恢复上传文件
tar -xzf /backup/pet-blog-system/uploads_latest.tar.gz

# 回滚代码版本
git reset --hard HEAD~1
```

这个部署配置指南提供了完整的生产环境部署方案，确保系统的稳定性、安全性和高性能。
