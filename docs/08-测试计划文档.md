# 宠物博客站群系统 - 测试计划文档

## 1. 测试概述

### 1.1 测试目标
- 确保系统功能完整性和正确性
- 验证多语言支持的准确性
- 保证SEO优化效果
- 确认系统性能和稳定性
- 验证安全性和数据保护

### 1.2 测试范围
- 后端API功能测试
- 前端界面功能测试
- 多语言系统测试
- SEO功能测试
- 性能压力测试
- 安全性测试
- 兼容性测试

### 1.3 测试环境
```
开发环境: localhost:3000 (后端) + localhost:4321 (前端)
测试环境: test.petblog.com
生产环境: petblog.com, haustierblog.de, питомцыблог.ru
```

## 2. 单元测试

### 2.1 后端API单元测试

#### 测试框架配置
```javascript
// backend/tests/setup.js
import { beforeAll, afterAll, beforeEach } from '@jest/globals';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.TEST_DATABASE_URL
    }
  }
});

beforeAll(async () => {
  // 初始化测试数据库
  await prisma.$connect();
});

afterAll(async () => {
  // 清理测试数据库
  await prisma.$disconnect();
});

beforeEach(async () => {
  // 每个测试前清理数据
  await prisma.comment.deleteMany();
  await prisma.article.deleteMany();
  await prisma.category.deleteMany();
  await prisma.user.deleteMany();
});
```

#### 用户认证测试
```javascript
// backend/tests/auth.test.js
import request from 'supertest';
import app from '../src/app.js';

describe('Authentication API', () => {
  test('POST /api/v1/auth/login - 成功登录', async () => {
    // 创建测试用户
    await createTestUser();
    
    const response = await request(app)
      .post('/api/v1/auth/login')
      .send({
        username: 'testadmin',
        password: 'testpassword'
      });
    
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.data.token).toBeDefined();
    expect(response.body.data.user.username).toBe('testadmin');
  });
  
  test('POST /api/v1/auth/login - 错误密码', async () => {
    await createTestUser();
    
    const response = await request(app)
      .post('/api/v1/auth/login')
      .send({
        username: 'testadmin',
        password: 'wrongpassword'
      });
    
    expect(response.status).toBe(401);
    expect(response.body.success).toBe(false);
  });
  
  test('GET /api/v1/auth/verify - 验证有效Token', async () => {
    const { token } = await loginTestUser();
    
    const response = await request(app)
      .get('/api/v1/auth/verify')
      .set('Authorization', `Bearer ${token}`);
    
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
  });
});
```

#### 文章管理测试
```javascript
// backend/tests/articles.test.js
describe('Articles API', () => {
  let authToken;
  let testCategory;
  
  beforeEach(async () => {
    const { token } = await loginTestUser();
    authToken = token;
    testCategory = await createTestCategory();
  });
  
  test('POST /api/v1/admin/articles - 创建文章', async () => {
    const articleData = {
      title: '测试文章标题',
      content: '这是测试文章内容...',
      excerpt: '文章摘要',
      category_id: testCategory.id,
      language: 'zh',
      status: 'draft'
    };
    
    const response = await request(app)
      .post('/api/v1/admin/articles')
      .set('Authorization', `Bearer ${authToken}`)
      .send(articleData);
    
    expect(response.status).toBe(201);
    expect(response.body.success).toBe(true);
    expect(response.body.data.title).toBe(articleData.title);
  });
  
  test('GET /api/v1/articles - 获取文章列表', async () => {
    await createTestArticles(5);
    
    const response = await request(app)
      .get('/api/v1/articles?page=1&limit=10&language=zh');
    
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.data.articles).toHaveLength(5);
    expect(response.body.data.pagination).toBeDefined();
  });
  
  test('POST /api/v1/admin/articles/{id}/translate - 翻译文章', async () => {
    const article = await createTestArticle();
    
    const response = await request(app)
      .post(`/api/v1/admin/articles/${article.id}/translate`)
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        target_languages: ['en', 'de']
      });
    
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
  });
});
```

#### 评论系统测试
```javascript
// backend/tests/comments.test.js
describe('Comments API', () => {
  let testArticle;
  
  beforeEach(async () => {
    testArticle = await createTestArticle();
  });
  
  test('POST /api/v1/comments - 提交评论', async () => {
    const commentData = {
      article_id: testArticle.id,
      parent_id: 0,
      content: '这是一条测试评论',
      author_name: '测试用户',
      author_email: '<EMAIL>'
    };
    
    const response = await request(app)
      .post('/api/v1/comments')
      .send(commentData);
    
    expect(response.status).toBe(201);
    expect(response.body.success).toBe(true);
    expect(response.body.data.status).toBe('pending');
  });
  
  test('GET /api/v1/comments - 获取评论列表', async () => {
    await createTestComments(testArticle.id, 3);
    
    const response = await request(app)
      .get(`/api/v1/comments?article_id=${testArticle.id}&status=approved`);
    
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
  });
});
```

### 2.2 前端组件单元测试

#### 测试框架配置
```javascript
// frontend/tests/setup.js
import { expect, afterEach } from 'vitest';
import { cleanup } from '@testing-library/astro';
import matchers from '@testing-library/jest-dom/matchers';

expect.extend(matchers);

afterEach(() => {
  cleanup();
});
```

#### 组件测试示例
```javascript
// frontend/tests/components/ArticleCard.test.js
import { render, screen } from '@testing-library/astro';
import ArticleCard from '../../src/shared/components/ArticleCard.astro';

describe('ArticleCard Component', () => {
  const mockArticle = {
    id: 1,
    title: 'Test Article Title',
    excerpt: 'This is a test excerpt',
    slug: 'test-article-title',
    featured_image: '/images/test.jpg',
    category: {
      name: 'Cat Care',
      slug: 'cat-care'
    },
    published_at: '2024-01-15T00:00:00.000Z',
    view_count: 100
  };
  
  test('渲染文章卡片基本信息', async () => {
    render(ArticleCard, {
      props: {
        article: mockArticle,
        language: 'en'
      }
    });
    
    expect(screen.getByText('Test Article Title')).toBeInTheDocument();
    expect(screen.getByText('This is a test excerpt')).toBeInTheDocument();
    expect(screen.getByText('Cat Care')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument();
  });
  
  test('文章链接正确', async () => {
    render(ArticleCard, {
      props: {
        article: mockArticle,
        language: 'en'
      }
    });
    
    const titleLink = screen.getByRole('link', { name: 'Test Article Title' });
    expect(titleLink).toHaveAttribute('href', '/articles/test-article-title');
  });
});
```

## 3. 集成测试

### 3.1 API集成测试
```javascript
// backend/tests/integration/workflow.test.js
describe('完整工作流程测试', () => {
  test('文章发布到翻译到显示的完整流程', async () => {
    // 1. 管理员登录
    const { token } = await loginTestUser();
    
    // 2. 创建分类
    const category = await createTestCategory();
    
    // 3. 创建文章
    const articleResponse = await request(app)
      .post('/api/v1/admin/articles')
      .set('Authorization', `Bearer ${token}`)
      .send({
        title: '如何照顾小猫',
        content: '详细的小猫护理指南...',
        category_id: category.id,
        language: 'zh',
        status: 'published'
      });
    
    const article = articleResponse.body.data;
    
    // 4. 翻译文章
    await request(app)
      .post(`/api/v1/admin/articles/${article.id}/translate`)
      .set('Authorization', `Bearer ${token}`)
      .send({
        target_languages: ['en']
      });
    
    // 5. 验证翻译结果
    const translatedResponse = await request(app)
      .get('/api/v1/articles')
      .query({ language: 'en', status: 'draft' });
    
    expect(translatedResponse.body.data.articles).toHaveLength(1);
    
    // 6. 发布翻译文章
    const translatedArticle = translatedResponse.body.data.articles[0];
    await request(app)
      .put(`/api/v1/admin/articles/${translatedArticle.id}`)
      .set('Authorization', `Bearer ${token}`)
      .send({ status: 'published' });
    
    // 7. 验证公开访问
    const publicResponse = await request(app)
      .get('/api/v1/articles')
      .query({ language: 'en', status: 'published' });
    
    expect(publicResponse.body.data.articles).toHaveLength(1);
  });
});
```

### 3.2 前后端集成测试
```javascript
// tests/e2e/article-management.test.js
import { test, expect } from '@playwright/test';

test.describe('文章管理端到端测试', () => {
  test('管理员可以创建、编辑和发布文章', async ({ page }) => {
    // 登录管理后台
    await page.goto('/admin/login');
    await page.fill('[name="username"]', 'testadmin');
    await page.fill('[name="password"]', 'testpassword');
    await page.click('button[type="submit"]');
    
    // 创建新文章
    await page.goto('/admin/articles/new');
    await page.fill('[name="title"]', '测试文章标题');
    await page.fill('[name="content"]', '这是测试文章内容');
    await page.selectOption('[name="category_id"]', '1');
    await page.click('button:has-text("保存草稿")');
    
    // 验证文章创建成功
    await expect(page.locator('.success-message')).toContainText('文章保存成功');
    
    // 发布文章
    await page.click('button:has-text("发布文章")');
    await expect(page.locator('.article-status')).toContainText('已发布');
    
    // 验证前端显示
    await page.goto('/');
    await expect(page.locator('h3:has-text("测试文章标题")')).toBeVisible();
  });
});
```

## 4. 功能测试

### 4.1 多语言功能测试
```javascript
describe('多语言功能测试', () => {
  test('域名语言识别', async () => {
    // 测试英语域名
    const enResponse = await request(app)
      .get('/api/v1/articles')
      .set('Host', 'petblog.com');
    
    expect(enResponse.body.data.articles[0].language).toBe('en');
    
    // 测试德语域名
    const deResponse = await request(app)
      .get('/api/v1/articles')
      .set('Host', 'haustierblog.de');
    
    expect(deResponse.body.data.articles[0].language).toBe('de');
  });
  
  test('翻译API集成', async () => {
    const translationResponse = await request(app)
      .post('/api/v1/admin/translate')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        text: '如何照顾小猫',
        target_language: 'en'
      });
    
    expect(translationResponse.status).toBe(200);
    expect(translationResponse.body.data.translated_text).toContain('kitten');
  });
});
```

### 4.2 SEO功能测试
```javascript
describe('SEO功能测试', () => {
  test('网站地图生成', async () => {
    const response = await request(app)
      .get('/sitemap.xml')
      .set('Host', 'petblog.com');
    
    expect(response.status).toBe(200);
    expect(response.headers['content-type']).toContain('application/xml');
    expect(response.text).toContain('<urlset');
    expect(response.text).toContain('petblog.com');
  });
  
  test('结构化数据生成', async () => {
    const article = await createTestArticle();
    
    const response = await request(app)
      .get(`/articles/${article.slug}`)
      .set('Host', 'petblog.com');
    
    expect(response.text).toContain('application/ld+json');
    expect(response.text).toContain('"@type": "Article"');
  });
});
```

## 5. 性能测试

### 5.1 负载测试配置
```javascript
// tests/performance/load-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // 2分钟内增加到100用户
    { duration: '5m', target: 100 }, // 保持100用户5分钟
    { duration: '2m', target: 200 }, // 2分钟内增加到200用户
    { duration: '5m', target: 200 }, // 保持200用户5分钟
    { duration: '2m', target: 0 },   // 2分钟内减少到0用户
  ],
  thresholds: {
    http_req_duration: ['p(99)<1500'], // 99%的请求在1.5秒内完成
    http_req_failed: ['rate<0.1'],     // 错误率小于10%
  },
};

export default function () {
  // 测试首页加载
  let response = http.get('https://petblog.com/');
  check(response, {
    '首页状态码为200': (r) => r.status === 200,
    '首页加载时间<2秒': (r) => r.timings.duration < 2000,
  });
  
  // 测试文章列表API
  response = http.get('https://petblog.com/api/v1/articles');
  check(response, {
    'API状态码为200': (r) => r.status === 200,
    'API响应时间<500ms': (r) => r.timings.duration < 500,
  });
  
  sleep(1);
}
```

### 5.2 数据库性能测试
```javascript
describe('数据库性能测试', () => {
  test('文章查询性能', async () => {
    // 创建大量测试数据
    await createTestArticles(1000);
    
    const startTime = Date.now();
    
    const response = await request(app)
      .get('/api/v1/articles?page=1&limit=20');
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    expect(response.status).toBe(200);
    expect(duration).toBeLessThan(500); // 查询时间小于500ms
  });
  
  test('搜索功能性能', async () => {
    await createTestArticles(1000);
    
    const startTime = Date.now();
    
    const response = await request(app)
      .get('/api/v1/search?q=cat&language=en');
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    expect(response.status).toBe(200);
    expect(duration).toBeLessThan(1000); // 搜索时间小于1秒
  });
});
```

## 6. 安全测试

### 6.1 认证安全测试
```javascript
describe('安全测试', () => {
  test('SQL注入防护', async () => {
    const maliciousInput = "'; DROP TABLE articles; --";
    
    const response = await request(app)
      .get('/api/v1/search')
      .query({ q: maliciousInput });
    
    expect(response.status).toBe(200);
    // 验证数据库表仍然存在
    const articlesResponse = await request(app)
      .get('/api/v1/articles');
    expect(articlesResponse.status).toBe(200);
  });
  
  test('XSS防护', async () => {
    const xssPayload = '<script>alert("XSS")</script>';
    
    const response = await request(app)
      .post('/api/v1/comments')
      .send({
        article_id: 1,
        content: xssPayload,
        author_name: 'Test User',
        author_email: '<EMAIL>'
      });
    
    expect(response.status).toBe(201);
    
    // 验证内容被正确转义
    const commentsResponse = await request(app)
      .get('/api/v1/comments?article_id=1');
    
    expect(commentsResponse.body.data.comments[0].content)
      .not.toContain('<script>');
  });
  
  test('未授权访问防护', async () => {
    const response = await request(app)
      .post('/api/v1/admin/articles')
      .send({
        title: 'Unauthorized Article',
        content: 'This should not be created'
      });
    
    expect(response.status).toBe(401);
  });
});
```

## 7. 兼容性测试

### 7.1 浏览器兼容性测试
```javascript
// tests/compatibility/browser.test.js
import { test, devices } from '@playwright/test';

const browsers = [
  'Desktop Chrome',
  'Desktop Firefox',
  'Desktop Safari',
  'Mobile Chrome',
  'Mobile Safari'
];

browsers.forEach(browserName => {
  test.describe(`${browserName} 兼容性测试`, () => {
    test.use({ ...devices[browserName] });
    
    test('首页正常加载', async ({ page }) => {
      await page.goto('/');
      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('nav')).toBeVisible();
    });
    
    test('文章页面正常显示', async ({ page }) => {
      await page.goto('/articles/test-article');
      await expect(page.locator('article')).toBeVisible();
      await expect(page.locator('.article-content')).toBeVisible();
    });
  });
});
```

## 8. 测试执行计划

### 8.1 测试阶段安排
```
阶段1: 单元测试 (开发过程中持续进行)
- 后端API单元测试
- 前端组件单元测试
- 覆盖率目标: >80%

阶段2: 集成测试 (功能开发完成后)
- API集成测试
- 前后端集成测试
- 多语言功能测试

阶段3: 系统测试 (系统集成完成后)
- 功能完整性测试
- SEO功能验证
- 用户体验测试

阶段4: 性能测试 (系统测试通过后)
- 负载测试
- 压力测试
- 数据库性能测试

阶段5: 安全测试 (部署前)
- 安全漏洞扫描
- 渗透测试
- 数据保护验证

阶段6: 验收测试 (部署后)
- 生产环境验证
- 用户验收测试
- 监控指标验证
```

### 8.2 测试自动化
```bash
# package.json scripts
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:e2e": "playwright test",
    "test:load": "k6 run tests/performance/load-test.js",
    "test:all": "npm run test && npm run test:e2e"
  }
}
```

这个测试计划确保了系统的质量和可靠性，涵盖了功能、性能、安全等各个方面的测试需求。
