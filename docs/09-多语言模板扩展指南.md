# 宠物博客站群系统 - 多语言模板扩展指南

## 1. 多语言架构概述

### 1.1 设计理念
- **一语言一模板**: 每种语言独立的前端模板，非i18n方案
- **域名绑定**: 每个顶级域名对应一种语言
- **内容本地化**: 不仅翻译文字，还要适应当地文化
- **SEO优化**: 每种语言独立的SEO策略
- **扩展性**: 支持快速添加新语言

### 1.2 当前支持语言
```
已实现语言:
- 中文 (zh) - 原创语言
- 英语 (en) - petblog.com
- 德语 (de) - haustierblog.de  
- 俄语 (ru) - питомцыблог.ru

计划扩展语言:
- 法语 (fr) - bloganimaux.fr
- 西班牙语 (es) - blogmascotas.es
- 日语 (ja) - ペットブログ.jp
- 韩语 (ko) - 펫블로그.kr
```

## 2. 新语言添加流程

### 2.1 准备工作清单
```
□ 确定目标语言和地区
□ 注册对应域名
□ 准备语言代码 (ISO 639-1)
□ 收集本地化内容需求
□ 确定文化适应性要求
□ 准备翻译资源
```

### 2.2 数据库配置

#### 添加新语言到数据库
```sql
-- 1. 添加语言配置
INSERT INTO languages (code, name, native_name, is_default, is_active, sort_order) 
VALUES ('fr', 'French', 'Français', FALSE, TRUE, 5);

-- 2. 添加域名映射
INSERT INTO domain_mappings (domain, language_code, is_active) 
VALUES ('bloganimaux.fr', 'fr', TRUE);

-- 3. 创建基础分类 (法语示例)
INSERT INTO categories (name, slug, language_code, sort_order, is_active) VALUES
('Soins des chats', 'soins-des-chats', 'fr', 1, TRUE),
('Dressage de chiens', 'dressage-de-chiens', 'fr', 2, TRUE),
('Santé des animaux', 'sante-des-animaux', 'fr', 3, TRUE),
('Accessoires pour animaux', 'accessoires-pour-animaux', 'fr', 4, TRUE);
```

### 2.3 前端模板创建

#### 步骤1: 复制基础模板
```bash
# 复制英语模板作为基础
cp -r frontend/src/templates/en frontend/src/templates/fr

# 重命名页面路径文件
cd frontend/src/templates/fr/pages
mv articles articles-backup
mkdir articles
# 根据法语习惯调整路径结构
```

#### 步骤2: 本地化配置文件
创建 `frontend/src/templates/fr/config/locale.ts`:
```typescript
export const frLocale = {
  // 基础信息
  language: 'fr',
  region: 'FR',
  direction: 'ltr',
  
  // 网站信息
  siteName: 'Blog Animaux',
  siteDescription: 'Votre guide complet pour le soin des animaux de compagnie',
  
  // 导航菜单
  navigation: {
    home: 'Accueil',
    categories: 'Catégories',
    about: 'À propos',
    contact: 'Contact',
    search: 'Rechercher'
  },
  
  // 分类名称
  categories: {
    'cat-care': 'Soins des chats',
    'dog-training': 'Dressage de chiens',
    'pet-health': 'Santé des animaux',
    'pet-supplies': 'Accessoires pour animaux'
  },
  
  // 通用文本
  common: {
    readMore: 'Lire la suite',
    publishedOn: 'Publié le',
    by: 'par',
    views: 'vues',
    comments: 'commentaires',
    categories: 'Catégories',
    tags: 'Étiquettes',
    share: 'Partager',
    previous: 'Précédent',
    next: 'Suivant',
    page: 'Page',
    of: 'de',
    noResults: 'Aucun résultat trouvé',
    loading: 'Chargement...',
    error: 'Une erreur est survenue'
  },
  
  // 表单文本
  forms: {
    name: 'Nom',
    email: 'E-mail',
    comment: 'Commentaire',
    submit: 'Envoyer',
    required: 'Requis',
    invalidEmail: 'E-mail invalide',
    commentSubmitted: 'Commentaire soumis avec succès',
    commentPending: 'Votre commentaire est en attente de modération'
  },
  
  // SEO相关
  seo: {
    defaultTitle: 'Blog Animaux - Guide complet pour animaux de compagnie',
    titleSeparator: ' | ',
    defaultDescription: 'Découvrez nos conseils d\'experts pour prendre soin de vos animaux de compagnie. Guides sur les chats, chiens, santé et bien-être animal.',
    keywords: 'animaux de compagnie, soins des chats, dressage de chiens, santé animale, conseils vétérinaires'
  },
  
  // 日期格式
  dateFormat: {
    short: 'dd/MM/yyyy',
    long: 'dd MMMM yyyy',
    locale: 'fr-FR'
  },
  
  // 数字格式
  numberFormat: {
    locale: 'fr-FR',
    currency: 'EUR'
  }
};
```

#### 步骤3: 更新布局组件
修改 `frontend/src/templates/fr/layouts/Layout.astro`:
```astro
---
import { frLocale } from '../config/locale';
import BaseLayout from '../../../shared/layouts/BaseLayout.astro';

export interface Props {
  title?: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  canonicalUrl?: string;
  noindex?: boolean;
}

const {
  title = frLocale.seo.defaultTitle,
  description = frLocale.seo.defaultDescription,
  keywords = frLocale.seo.keywords,
  ogImage,
  canonicalUrl,
  noindex = false
} = Astro.props;

const fullTitle = title === frLocale.seo.defaultTitle 
  ? title 
  : `${title}${frLocale.seo.titleSeparator}${frLocale.siteName}`;
---

<BaseLayout
  title={fullTitle}
  description={description}
  keywords={keywords}
  ogImage={ogImage}
  canonicalUrl={canonicalUrl}
  language="fr"
  noindex={noindex}
>
  <slot />
</BaseLayout>
```

#### 步骤4: 本地化页面组件
修改 `frontend/src/templates/fr/pages/index.astro`:
```astro
---
import Layout from '../layouts/Layout.astro';
import Header from '../../../shared/components/Header.astro';
import Footer from '../../../shared/components/Footer.astro';
import ArticleCard from '../../../shared/components/ArticleCard.astro';
import { frLocale } from '../config/locale';

// 获取文章数据
const articlesResponse = await fetch(`${import.meta.env.API_URL}/api/v1/articles?language=fr&status=published&limit=12`);
const articlesData = await articlesResponse.json();
const articles = articlesData.success ? articlesData.data.articles : [];

// 获取分类数据
const categoriesResponse = await fetch(`${import.meta.env.API_URL}/api/v1/categories?language=fr`);
const categoriesData = await categoriesResponse.json();
const categories = categoriesData.success ? categoriesData.data : [];
---

<Layout>
  <Header 
    language="fr"
    siteName={frLocale.siteName}
    navigation={[
      { name: frLocale.navigation.home, href: '/', current: true },
      { name: frLocale.navigation.categories, href: '/categories' },
      { name: frLocale.navigation.about, href: '/a-propos' },
      { name: frLocale.navigation.contact, href: '/contact' }
    ]}
  />
  
  <main>
    <!-- 英雄区域 -->
    <section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          {frLocale.siteName}
        </h1>
        <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
          {frLocale.siteDescription}
        </p>
        <a 
          href="/categories" 
          class="inline-block bg-white text-blue-600 font-semibold px-8 py-3 rounded-lg hover:bg-gray-100 transition-colors"
        >
          Découvrir nos guides
        </a>
      </div>
    </section>
    
    <!-- 最新文章 -->
    <section class="py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 mb-8">Articles récents</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {articles.map((article) => (
            <ArticleCard article={article} language="fr" />
          ))}
        </div>
      </div>
    </section>
    
    <!-- 分类展示 -->
    <section class="bg-gray-50 py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Nos catégories</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {categories.map((category) => (
            <a 
              href={`/categorie/${category.slug}`}
              class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow text-center"
            >
              <h3 class="text-xl font-semibold text-gray-900 mb-2">{category.name}</h3>
              <p class="text-gray-600">{category.description}</p>
            </a>
          ))}
        </div>
      </div>
    </section>
  </main>
  
  <Footer language="fr" />
</Layout>
```

### 2.4 URL路径本地化

#### 法语路径映射
```typescript
// frontend/src/templates/fr/config/routes.ts
export const frRoutes = {
  // 基础路径
  home: '/',
  
  // 文章相关
  articles: '/articles',
  article: '/articles/[slug]',
  
  // 分类相关  
  categories: '/categories',
  category: '/categorie/[slug]',
  
  // 搜索
  search: '/recherche',
  
  // 静态页面
  about: '/a-propos',
  privacy: '/confidentialite', 
  contact: '/contact',
  
  // 特殊页面
  sitemap: '/plan-du-site',
  rss: '/rss.xml'
};
```

#### 创建本地化页面文件
```bash
# 创建法语特定的页面文件
frontend/src/templates/fr/pages/
├── index.astro                    # 首页
├── articles/
│   └── [slug].astro              # 文章详情
├── categorie/
│   └── [slug].astro              # 分类页面 (注意法语路径)
├── categories.astro               # 分类列表
├── recherche.astro                # 搜索页面 (法语)
├── a-propos.astro                 # 关于页面 (法语)
├── confidentialite.astro          # 隐私政策 (法语)
├── contact.astro                  # 联系页面
└── 404.astro                      # 404页面
```

## 3. 内容本地化策略

### 3.1 文化适应性考虑

#### 法语市场特点
```typescript
// frontend/src/templates/fr/config/cultural.ts
export const frCultural = {
  // 宠物文化差异
  petCulture: {
    popularPets: ['chats', 'chiens', 'lapins', 'oiseaux'],
    commonBreeds: {
      cats: ['Chartreux', 'Maine Coon', 'Persan', 'Siamois'],
      dogs: ['Bouledogue français', 'Berger allemand', 'Golden Retriever', 'Labrador']
    },
    veterinarySystem: 'Système vétérinaire français',
    petInsurance: 'Assurance animaux en France'
  },
  
  // 法律法规
  regulations: {
    petOwnership: 'Réglementation française sur les animaux de compagnie',
    vaccination: 'Calendrier de vaccination obligatoire en France',
    travel: 'Voyager avec des animaux en Europe'
  },
  
  // 本地化内容主题
  localTopics: [
    'Réglementation française sur les animaux',
    'Vétérinaires en France',
    'Assurance pour animaux de compagnie',
    'Voyager avec des animaux en Europe',
    'Races de chiens populaires en France',
    'Alimentation bio pour animaux'
  ]
};
```

### 3.2 翻译质量控制

#### 翻译工作流程
```
1. 机器翻译 (Gemini API)
   ↓
2. 专业校对
   ↓  
3. 文化适应性调整
   ↓
4. SEO优化
   ↓
5. 最终审核
   ↓
6. 发布
```

#### 翻译质量检查清单
```
□ 术语一致性
□ 语法正确性  
□ 文化适应性
□ SEO关键词优化
□ 链接本地化
□ 图片alt文本翻译
□ 元数据翻译
□ 结构化数据本地化
```

## 4. SEO本地化

### 4.1 关键词本地化
```typescript
// frontend/src/templates/fr/config/seo.ts
export const frSEO = {
  // 主要关键词
  primaryKeywords: [
    'soins des chats',
    'dressage de chiens', 
    'santé des animaux',
    'vétérinaire',
    'animaux de compagnie'
  ],
  
  // 长尾关键词
  longTailKeywords: [
    'comment dresser un chiot',
    'alimentation chat senior',
    'vaccins obligatoires chien France',
    'assurance animaux de compagnie',
    'urgence vétérinaire Paris'
  ],
  
  // 本地化关键词
  localKeywords: [
    'vétérinaire Paris',
    'clinique vétérinaire Lyon',
    'animalerie Marseille',
    'pension pour chiens Toulouse',
    'toilettage chat Bordeaux'
  ]
};
```

### 4.2 结构化数据本地化
```json
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "Comment prendre soin d'un chaton",
  "description": "Guide complet pour prendre soin d'un chaton : alimentation, soins, vaccination et conseils d'experts.",
  "inLanguage": "fr-FR",
  "author": {
    "@type": "Person",
    "name": "Expert en soins animaliers"
  },
  "publisher": {
    "@type": "Organization", 
    "name": "Blog Animaux",
    "logo": {
      "@type": "ImageObject",
      "url": "https://bloganimaux.fr/logo.png"
    }
  },
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://bloganimaux.fr/articles/comment-prendre-soin-chaton"
  },
  "articleSection": "Soins des chats",
  "keywords": ["chaton", "soins", "alimentation", "vaccination", "santé"]
}
```

## 5. 技术实现

### 5.1 Astro配置更新
修改 `frontend/astro.config.mjs`:
```javascript
import { defineConfig } from 'astro/config';

export default defineConfig({
  // 多站点配置
  site: process.env.SITE_URL || 'https://petblog.com',
  
  // 构建配置
  build: {
    format: 'directory'
  },
  
  // 多语言路由
  i18n: {
    defaultLocale: 'en',
    locales: ['en', 'de', 'ru', 'fr'],
    routing: {
      prefixDefaultLocale: false
    }
  }
});
```

### 5.2 构建脚本更新
修改 `scripts/build-sites.sh`:
```bash
#!/bin/bash

LANGUAGES=("en" "de" "ru" "fr")
DOMAINS=("petblog.com" "haustierblog.de" "питомцыблог.ru" "bloganimaux.fr")

for i in "${!LANGUAGES[@]}"; do
  LANG="${LANGUAGES[$i]}"
  DOMAIN="${DOMAINS[$i]}"
  
  echo "Building ${LANG} site for ${DOMAIN}..."
  
  cd frontend
  LANGUAGE=$LANG DOMAIN=$DOMAIN npm run build
  mv dist ../dist-$LANG
  cd ..
  
  echo "${LANG} site built successfully!"
done

echo "All sites built successfully!"
```

### 5.3 Nginx配置更新
添加新语言站点配置:
```nginx
# 法语站点配置
server {
    listen 80;
    listen 443 ssl http2;
    server_name bloganimaux.fr www.bloganimaux.fr;
    
    # SSL配置
    ssl_certificate /path/to/ssl/bloganimaux.fr.crt;
    ssl_certificate_key /path/to/ssl/bloganimaux.fr.key;
    
    # 静态文件
    location / {
        root /www/wwwroot/pet-blog-system/dist-fr;
        try_files $uri $uri/ $uri.html /404.html;
    }
    
    # API代理 (共享后端)
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 其他配置...
}
```

## 6. 测试与验证

### 6.1 新语言测试清单
```
□ 域名解析正确
□ 语言识别正确
□ 页面正常加载
□ 导航菜单本地化
□ 内容显示正确
□ 搜索功能正常
□ 评论系统工作
□ SEO标签正确
□ 网站地图生成
□ 移动端适配
□ 性能测试通过
```

### 6.2 自动化测试
```javascript
// tests/languages/fr.test.js
describe('法语站点测试', () => {
  test('法语域名访问', async () => {
    const response = await request(app)
      .get('/')
      .set('Host', 'bloganimaux.fr');
    
    expect(response.status).toBe(200);
    expect(response.text).toContain('lang="fr"');
    expect(response.text).toContain('Blog Animaux');
  });
  
  test('法语内容API', async () => {
    const response = await request(app)
      .get('/api/v1/articles')
      .set('Host', 'bloganimaux.fr');
    
    expect(response.body.data.articles[0].language).toBe('fr');
  });
});
```

## 7. 维护与更新

### 7.1 内容同步策略
```
新文章发布流程:
1. 中文原创文章发布
2. 自动翻译为所有目标语言
3. 各语言编辑校对
4. 逐步发布到各语言站点
```

### 7.2 性能监控
```javascript
// 监控各语言站点性能
const sites = [
  'https://petblog.com',
  'https://haustierblog.de', 
  'https://питомцыблог.ru',
  'https://bloganimaux.fr'
];

sites.forEach(site => {
  // 监控加载速度、可用性等指标
});
```

这个多语言模板扩展指南提供了完整的新语言添加流程，确保每个新语言站点都能保持高质量和一致性。
