# 宠物博客站群系统 - 数据库设计文档

## 1. 数据库概述

### 1.1 基础信息
- **数据库类型**: MySQL 5.7.44
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB
- **连接信息**: 
  - 服务器: 23.236.69.41
  - 数据库: bengtai
  - 用户: bengtai

### 1.2 设计原则
- 支持多语言内容存储
- 优化查询性能
- 保证数据一致性
- 支持水平扩展
- 遵循第三范式

## 2. 数据库表结构

### 2.1 用户管理表

#### users (用户表)
```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
  email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
  password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
  role ENUM('admin', 'editor') DEFAULT 'admin' COMMENT '角色',
  status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
  last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_username (username),
  INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

### 2.2 语言和域名配置表

#### languages (语言配置表)
```sql
CREATE TABLE languages (
  id INT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(5) NOT NULL UNIQUE COMMENT '语言代码(en,de,ru)',
  name VARCHAR(50) NOT NULL COMMENT '语言名称',
  native_name VARCHAR(50) NOT NULL COMMENT '本地语言名称',
  is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认语言',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  sort_order INT DEFAULT 0 COMMENT '排序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_code (code),
  INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='语言配置表';
```

#### domain_mappings (域名映射表)
```sql
CREATE TABLE domain_mappings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  domain VARCHAR(100) NOT NULL UNIQUE COMMENT '域名',
  language_code VARCHAR(5) NOT NULL COMMENT '对应语言代码',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (language_code) REFERENCES languages(code) ON UPDATE CASCADE,
  INDEX idx_domain (domain),
  INDEX idx_language (language_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名语言映射表';
```

### 2.3 分类管理表

#### categories (分类表)
```sql
CREATE TABLE categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '分类名称',
  slug VARCHAR(100) NOT NULL COMMENT 'URL别名',
  description TEXT COMMENT '分类描述',
  parent_id INT DEFAULT 0 COMMENT '父分类ID',
  language_code VARCHAR(5) NOT NULL COMMENT '语言代码',
  sort_order INT DEFAULT 0 COMMENT '排序',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  seo_title VARCHAR(200) COMMENT 'SEO标题',
  seo_description VARCHAR(300) COMMENT 'SEO描述',
  seo_keywords VARCHAR(500) COMMENT 'SEO关键词',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (language_code) REFERENCES languages(code) ON UPDATE CASCADE,
  UNIQUE KEY uk_slug_lang (slug, language_code),
  INDEX idx_parent (parent_id),
  INDEX idx_language (language_code),
  INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';
```

### 2.4 文章管理表

#### articles (文章表)
```sql
CREATE TABLE articles (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(200) NOT NULL COMMENT '文章标题',
  slug VARCHAR(200) NOT NULL COMMENT 'URL别名',
  excerpt TEXT COMMENT '文章摘要',
  content LONGTEXT NOT NULL COMMENT '文章内容',
  featured_image VARCHAR(500) COMMENT '特色图片',
  category_id INT NOT NULL COMMENT '分类ID',
  language_code VARCHAR(5) NOT NULL COMMENT '语言代码',
  original_article_id INT NULL COMMENT '原文章ID(翻译关联)',
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft' COMMENT '状态',
  view_count INT DEFAULT 0 COMMENT '浏览次数',
  comment_count INT DEFAULT 0 COMMENT '评论数量',
  is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
  published_at TIMESTAMP NULL COMMENT '发布时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
  FOREIGN KEY (language_code) REFERENCES languages(code) ON UPDATE CASCADE,
  FOREIGN KEY (original_article_id) REFERENCES articles(id) ON DELETE SET NULL,
  UNIQUE KEY uk_slug_lang (slug, language_code),
  INDEX idx_category (category_id),
  INDEX idx_language (language_code),
  INDEX idx_status (status),
  INDEX idx_published (published_at),
  INDEX idx_featured (is_featured),
  FULLTEXT KEY ft_title_content (title, content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章表';
```

#### article_seo (文章SEO表)
```sql
CREATE TABLE article_seo (
  id INT PRIMARY KEY AUTO_INCREMENT,
  article_id INT NOT NULL COMMENT '文章ID',
  meta_title VARCHAR(200) COMMENT 'SEO标题',
  meta_description VARCHAR(300) COMMENT 'SEO描述',
  meta_keywords VARCHAR(500) COMMENT 'SEO关键词',
  og_title VARCHAR(200) COMMENT 'Open Graph标题',
  og_description VARCHAR(300) COMMENT 'Open Graph描述',
  og_image VARCHAR(500) COMMENT 'Open Graph图片',
  twitter_title VARCHAR(200) COMMENT 'Twitter标题',
  twitter_description VARCHAR(300) COMMENT 'Twitter描述',
  canonical_url VARCHAR(500) COMMENT '规范URL',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
  UNIQUE KEY uk_article (article_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章SEO信息表';
```

### 2.5 评论管理表

#### comments (评论表)
```sql
CREATE TABLE comments (
  id INT PRIMARY KEY AUTO_INCREMENT,
  article_id INT NOT NULL COMMENT '文章ID',
  parent_id INT DEFAULT 0 COMMENT '父评论ID',
  author_name VARCHAR(50) NOT NULL COMMENT '评论者姓名',
  author_email VARCHAR(100) NOT NULL COMMENT '评论者邮箱',
  author_ip VARCHAR(45) COMMENT '评论者IP',
  content TEXT NOT NULL COMMENT '评论内容',
  status ENUM('pending', 'approved', 'rejected', 'spam') DEFAULT 'pending' COMMENT '状态',
  reply_count INT DEFAULT 0 COMMENT '回复数量',
  user_agent TEXT COMMENT '用户代理',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
  INDEX idx_article (article_id),
  INDEX idx_parent (parent_id),
  INDEX idx_status (status),
  INDEX idx_created (created_at),
  INDEX idx_email (author_email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论表';
```

### 2.6 文件管理表

#### uploads (上传文件表)
```sql
CREATE TABLE uploads (
  id INT PRIMARY KEY AUTO_INCREMENT,
  filename VARCHAR(255) NOT NULL COMMENT '文件名',
  original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
  file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
  file_size INT NOT NULL COMMENT '文件大小(字节)',
  mime_type VARCHAR(100) NOT NULL COMMENT 'MIME类型',
  file_type ENUM('image', 'document', 'video', 'audio') NOT NULL COMMENT '文件类型',
  upload_type ENUM('article', 'category', 'avatar', 'other') DEFAULT 'other' COMMENT '上传类型',
  user_id INT COMMENT '上传用户ID',
  is_used BOOLEAN DEFAULT FALSE COMMENT '是否被使用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_type (file_type),
  INDEX idx_upload_type (upload_type),
  INDEX idx_user (user_id),
  INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='上传文件表';
```

### 2.7 系统配置表

#### system_configs (系统配置表)
```sql
CREATE TABLE system_configs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
  config_value TEXT COMMENT '配置值',
  config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
  description VARCHAR(500) COMMENT '配置描述',
  is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开(前端可访问)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_key (config_key),
  INDEX idx_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';
```

## 3. 数据库索引优化

### 3.1 主要索引策略
- 主键索引：所有表的id字段
- 唯一索引：用户名、邮箱、slug等唯一字段
- 复合索引：多字段查询优化
- 全文索引：文章标题和内容搜索

### 3.2 查询优化索引
```sql
-- 文章列表查询优化
CREATE INDEX idx_articles_list ON articles(language_code, status, published_at DESC);

-- 分类文章查询优化
CREATE INDEX idx_articles_category ON articles(category_id, status, published_at DESC);

-- 评论查询优化
CREATE INDEX idx_comments_article_status ON comments(article_id, status, created_at DESC);

-- 搜索优化
CREATE INDEX idx_articles_search ON articles(language_code, status, title);
```

## 4. 数据库初始化

### 4.1 默认数据插入
```sql
-- 插入默认语言
INSERT INTO languages (code, name, native_name, is_default, sort_order) VALUES
('zh', 'Chinese', '中文', TRUE, 1),
('en', 'English', 'English', FALSE, 2),
('de', 'German', 'Deutsch', FALSE, 3),
('ru', 'Russian', 'Русский', FALSE, 4);

-- 插入默认管理员
INSERT INTO users (username, email, password_hash, role) VALUES
('admin', '<EMAIL>', '$2b$10$...', 'admin');

-- 插入默认分类
INSERT INTO categories (name, slug, language_code, sort_order) VALUES
('猫咪护理', 'cat-care', 'zh', 1),
('狗狗训练', 'dog-training', 'zh', 2),
('宠物健康', 'pet-health', 'zh', 3),
('宠物用品', 'pet-supplies', 'zh', 4);

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, config_type, description, is_public) VALUES
('site_name', '宠物博客', 'string', '网站名称', TRUE),
('site_description', '专业的宠物知识分享平台', 'string', '网站描述', TRUE),
('google_analytics_id', '', 'string', 'Google Analytics ID', FALSE),
('google_adsense_client', '', 'string', 'Google AdSense客户端ID', FALSE);
```

## 5. 数据库维护

### 5.1 定期维护任务
- 清理过期的草稿文章
- 优化数据库表
- 更新统计数据
- 备份重要数据

### 5.2 性能监控
- 慢查询日志监控
- 索引使用情况分析
- 表空间使用监控
- 连接数监控

这个数据库设计支持多语言内容管理、高效查询和良好的扩展性，为整个系统提供了坚实的数据基础。
