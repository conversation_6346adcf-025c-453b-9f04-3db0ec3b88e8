# 宠物博客站群系统 - 前端组件设计文档

## 1. 前端架构概述

### 1.1 技术栈
- **框架**: Astro 4.x
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **图标**: Heroicons
- **字体**: Inter (英文) + <PERSON>o Sans (多语言)

### 1.2 项目结构
```
frontend/
├── src/
│   ├── templates/              # 多语言模板
│   │   ├── en/                # 英语模板
│   │   │   ├── layouts/       # 布局组件
│   │   │   ├── pages/         # 页面组件
│   │   │   └── components/    # 页面特定组件
│   │   ├── de/                # 德语模板
│   │   └── ru/                # 俄语模板
│   ├── shared/                # 共享组件
│   │   ├── components/        # 通用组件
│   │   ├── layouts/           # 基础布局
│   │   ├── utils/             # 工具函数
│   │   └── styles/            # 全局样式
│   ├── config/                # 配置文件
│   └── types/                 # TypeScript类型定义
├── public/                    # 静态资源
└── astro.config.mjs          # Astro配置
```

## 2. 共享组件设计

### 2.1 基础布局组件

#### BaseLayout.astro
```typescript
---
export interface Props {
  title: string;
  description: string;
  keywords?: string;
  ogImage?: string;
  canonicalUrl?: string;
  language: 'en' | 'de' | 'ru';
  noindex?: boolean;
}

const {
  title,
  description,
  keywords,
  ogImage,
  canonicalUrl,
  language,
  noindex = false
} = Astro.props;
---

<!DOCTYPE html>
<html lang={language}>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{title}</title>
  <meta name="description" content={description}>
  {keywords && <meta name="keywords" content={keywords}>}
  {noindex && <meta name="robots" content="noindex, nofollow">}
  
  <!-- Open Graph -->
  <meta property="og:title" content={title}>
  <meta property="og:description" content={description}>
  <meta property="og:type" content="website">
  {ogImage && <meta property="og:image" content={ogImage}>}
  
  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content={title}>
  <meta name="twitter:description" content={description}>
  
  {canonicalUrl && <link rel="canonical" href={canonicalUrl}>}
  
  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-gray-50 text-gray-900">
  <slot />
</body>
</html>
```

#### Header.astro
```typescript
---
export interface Props {
  language: 'en' | 'de' | 'ru';
  siteName: string;
  navigation: Array<{
    name: string;
    href: string;
    current?: boolean;
  }>;
}

const { language, siteName, navigation } = Astro.props;
---

<header class="bg-white shadow-sm border-b border-gray-200">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- Logo -->
      <div class="flex-shrink-0">
        <a href="/" class="text-2xl font-bold text-blue-600">
          {siteName}
        </a>
      </div>
      
      <!-- Desktop Navigation -->
      <nav class="hidden md:flex space-x-8">
        {navigation.map((item) => (
          <a
            href={item.href}
            class={`px-3 py-2 text-sm font-medium transition-colors ${
              item.current
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-700 hover:text-blue-600'
            }`}
          >
            {item.name}
          </a>
        ))}
      </nav>
      
      <!-- Search Button -->
      <div class="flex items-center space-x-4">
        <button
          type="button"
          class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          aria-label="Search"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </button>
        
        <!-- Mobile menu button -->
        <button
          type="button"
          class="md:hidden p-2 text-gray-400 hover:text-gray-600"
          aria-label="Open menu"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</header>
```

### 2.2 文章相关组件

#### ArticleCard.astro
```typescript
---
export interface Props {
  article: {
    id: number;
    title: string;
    excerpt: string;
    slug: string;
    featured_image?: string;
    category: {
      name: string;
      slug: string;
    };
    published_at: string;
    view_count: number;
  };
  language: 'en' | 'de' | 'ru';
}

const { article, language } = Astro.props;

// 格式化日期的函数
const formatDate = (dateString: string, lang: string) => {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  };
  return date.toLocaleDateString(lang === 'en' ? 'en-US' : lang === 'de' ? 'de-DE' : 'ru-RU', options);
};
---

<article class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
  {article.featured_image && (
    <div class="aspect-w-16 aspect-h-9">
      <img
        src={article.featured_image}
        alt={article.title}
        class="w-full h-48 object-cover"
        loading="lazy"
      />
    </div>
  )}
  
  <div class="p-6">
    <!-- Category -->
    <div class="mb-3">
      <a
        href={`/category/${article.category.slug}`}
        class="inline-block px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded-full hover:bg-blue-100 transition-colors"
      >
        {article.category.name}
      </a>
    </div>
    
    <!-- Title -->
    <h3 class="mb-3">
      <a
        href={`/articles/${article.slug}`}
        class="text-xl font-semibold text-gray-900 hover:text-blue-600 transition-colors line-clamp-2"
      >
        {article.title}
      </a>
    </h3>
    
    <!-- Excerpt -->
    <p class="text-gray-600 mb-4 line-clamp-3">
      {article.excerpt}
    </p>
    
    <!-- Meta -->
    <div class="flex items-center justify-between text-sm text-gray-500">
      <time datetime={article.published_at}>
        {formatDate(article.published_at, language)}
      </time>
      <span class="flex items-center">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
        {article.view_count}
      </span>
    </div>
  </div>
</article>
```

#### Pagination.astro
```typescript
---
export interface Props {
  currentPage: number;
  totalPages: number;
  baseUrl: string;
  language: 'en' | 'de' | 'ru';
}

const { currentPage, totalPages, baseUrl, language } = Astro.props;

// 翻译文本
const translations = {
  en: { previous: 'Previous', next: 'Next', page: 'Page' },
  de: { previous: 'Zurück', next: 'Weiter', page: 'Seite' },
  ru: { previous: 'Назад', next: 'Далее', page: 'Страница' }
};

const t = translations[language];

// 生成页码数组
const generatePageNumbers = (current: number, total: number) => {
  const pages = [];
  const showPages = 5; // 显示的页码数量
  
  let start = Math.max(1, current - Math.floor(showPages / 2));
  let end = Math.min(total, start + showPages - 1);
  
  if (end - start + 1 < showPages) {
    start = Math.max(1, end - showPages + 1);
  }
  
  for (let i = start; i <= end; i++) {
    pages.push(i);
  }
  
  return pages;
};

const pageNumbers = generatePageNumbers(currentPage, totalPages);
---

{totalPages > 1 && (
  <nav class="flex items-center justify-center space-x-2 mt-8" aria-label="Pagination">
    <!-- Previous Button -->
    {currentPage > 1 ? (
      <a
        href={currentPage === 2 ? baseUrl : `${baseUrl}?page=${currentPage - 1}`}
        class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
      >
        {t.previous}
      </a>
    ) : (
      <span class="px-3 py-2 text-sm font-medium text-gray-400 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
        {t.previous}
      </span>
    )}
    
    <!-- Page Numbers -->
    {pageNumbers.map((page) => (
      page === currentPage ? (
        <span
          class="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md"
          aria-current="page"
        >
          {page}
        </span>
      ) : (
        <a
          href={page === 1 ? baseUrl : `${baseUrl}?page=${page}`}
          class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
        >
          {page}
        </a>
      )
    ))}
    
    <!-- Next Button -->
    {currentPage < totalPages ? (
      <a
        href={`${baseUrl}?page=${currentPage + 1}`}
        class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
      >
        {t.next}
      </a>
    ) : (
      <span class="px-3 py-2 text-sm font-medium text-gray-400 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
        {t.next}
      </span>
    )}
  </nav>
)}
```

### 2.3 评论系统组件

#### CommentForm.astro
```typescript
---
export interface Props {
  articleId: number;
  parentId?: number;
  language: 'en' | 'de' | 'ru';
}

const { articleId, parentId = 0, language } = Astro.props;

// 翻译文本
const translations = {
  en: {
    name: 'Name',
    email: 'Email',
    comment: 'Comment',
    submit: 'Submit Comment',
    nameRequired: 'Name is required',
    emailRequired: 'Valid email is required',
    commentRequired: 'Comment is required'
  },
  de: {
    name: 'Name',
    email: 'E-Mail',
    comment: 'Kommentar',
    submit: 'Kommentar senden',
    nameRequired: 'Name ist erforderlich',
    emailRequired: 'Gültige E-Mail ist erforderlich',
    commentRequired: 'Kommentar ist erforderlich'
  },
  ru: {
    name: 'Имя',
    email: 'Email',
    comment: 'Комментарий',
    submit: 'Отправить комментарий',
    nameRequired: 'Имя обязательно',
    emailRequired: 'Требуется действительный email',
    commentRequired: 'Комментарий обязателен'
  }
};

const t = translations[language];
---

<form class="comment-form bg-gray-50 p-6 rounded-lg" data-article-id={articleId} data-parent-id={parentId}>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
    <div>
      <label for="author_name" class="block text-sm font-medium text-gray-700 mb-2">
        {t.name} <span class="text-red-500">*</span>
      </label>
      <input
        type="text"
        id="author_name"
        name="author_name"
        required
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      />
    </div>
    
    <div>
      <label for="author_email" class="block text-sm font-medium text-gray-700 mb-2">
        {t.email} <span class="text-red-500">*</span>
      </label>
      <input
        type="email"
        id="author_email"
        name="author_email"
        required
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      />
    </div>
  </div>
  
  <div class="mb-4">
    <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
      {t.comment} <span class="text-red-500">*</span>
    </label>
    <textarea
      id="content"
      name="content"
      rows="4"
      required
      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
    ></textarea>
  </div>
  
  <div class="flex justify-end">
    <button
      type="submit"
      class="px-6 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
    >
      {t.submit}
    </button>
  </div>
</form>

<script>
  // 评论提交处理
  document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('.comment-form');
    
    forms.forEach(form => {
      form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const articleId = form.dataset.articleId;
        const parentId = form.dataset.parentId;
        
        const data = {
          article_id: parseInt(articleId),
          parent_id: parseInt(parentId),
          author_name: formData.get('author_name'),
          author_email: formData.get('author_email'),
          content: formData.get('content')
        };
        
        try {
          const response = await fetch('/api/v1/comments', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
          });
          
          if (response.ok) {
            // 显示成功消息
            alert('评论提交成功，等待审核后显示');
            form.reset();
          } else {
            alert('评论提交失败，请重试');
          }
        } catch (error) {
          alert('网络错误，请重试');
        }
      });
    });
  });
</script>
```

## 3. 语言模板结构

### 3.1 英语模板 (en/)
```
en/
├── layouts/
│   ├── Layout.astro          # 英语布局
│   └── ArticleLayout.astro   # 文章布局
├── pages/
│   ├── index.astro          # 首页
│   ├── articles/
│   │   └── [slug].astro     # 文章详情
│   ├── category/
│   │   └── [slug].astro     # 分类页面
│   ├── search.astro         # 搜索页面
│   ├── about.astro          # 关于页面
│   ├── privacy.astro        # 隐私政策
│   └── contact.astro        # 联系页面
└── components/
    ├── Hero.astro           # 首页英雄区
    ├── FeaturedArticles.astro # 推荐文章
    └── CategoryList.astro   # 分类列表
```

### 3.2 德语模板 (de/)
```
de/
├── layouts/
│   ├── Layout.astro          # 德语布局
│   └── ArticleLayout.astro   # 文章布局
├── pages/
│   ├── index.astro          # 首页
│   ├── artikel/             # 文章(德语路径)
│   │   └── [slug].astro
│   ├── kategorie/           # 分类(德语路径)
│   │   └── [slug].astro
│   ├── suche.astro          # 搜索页面
│   ├── ueber-uns.astro      # 关于页面
│   ├── datenschutz.astro    # 隐私政策
│   └── kontakt.astro        # 联系页面
└── components/
    └── ... (德语本地化组件)
```

### 3.3 俄语模板 (ru/)
```
ru/
├── layouts/
│   ├── Layout.astro          # 俄语布局
│   └── ArticleLayout.astro   # 文章布局
├── pages/
│   ├── index.astro          # 首页
│   ├── статьи/              # 文章(俄语路径)
│   │   └── [slug].astro
│   ├── категория/           # 分类(俄语路径)
│   │   └── [slug].astro
│   ├── поиск.astro          # 搜索页面
│   ├── о-нас.astro          # 关于页面
│   ├── конфиденциальность.astro # 隐私政策
│   └── контакты.astro       # 联系页面
└── components/
    └── ... (俄语本地化组件)
```

## 4. 响应式设计

### 4.1 断点设计
```css
/* Tailwind CSS 断点 */
sm: 640px   /* 小屏幕 */
md: 768px   /* 中等屏幕 */
lg: 1024px  /* 大屏幕 */
xl: 1280px  /* 超大屏幕 */
2xl: 1536px /* 2K屏幕 */
```

### 4.2 移动端优化
- 触摸友好的按钮尺寸(最小44px)
- 优化的字体大小和行高
- 简化的导航菜单
- 快速加载的图片

## 5. 性能优化

### 5.1 图片优化
- 使用WebP格式
- 响应式图片
- 懒加载
- 适当的压缩比例

### 5.2 代码优化
- CSS和JS最小化
- 关键CSS内联
- 非关键资源延迟加载
- 使用CDN加速

这个前端组件设计确保了多语言支持、良好的用户体验和优秀的SEO表现。
