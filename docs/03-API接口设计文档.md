# 宠物博客站群系统 - API接口设计文档

## 1. API概述

### 1.1 基础信息
- **API版本**: v1
- **基础URL**: `http://localhost:3000/api/v1`
- **认证方式**: JWT Bearer <PERSON>ken
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 通用响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 1.3 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {}
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 2. 认证相关API

### 2.1 管理员登录
```
POST /auth/login
```

**请求参数**:
```json
{
  "username": "admin",
  "password": "password123"
}
```

**响应数据**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "role": "admin"
    }
  }
}
```

### 2.2 管理员登出
```
POST /auth/logout
Authorization: Bearer {token}
```

### 2.3 验证Token
```
GET /auth/verify
Authorization: Bearer {token}
```

## 3. 文章管理API

### 3.1 获取文章列表
```
GET /articles?page=1&limit=10&category=1&language=en&status=published
```

**查询参数**:
- `page`: 页码(默认1)
- `limit`: 每页数量(默认10，最大50)
- `category`: 分类ID
- `language`: 语言代码(en/de/ru)
- `status`: 状态(draft/published/archived)
- `search`: 搜索关键词

**响应数据**:
```json
{
  "success": true,
  "data": {
    "articles": [
      {
        "id": 1,
        "title": "如何照顾小猫",
        "slug": "how-to-care-for-kittens",
        "excerpt": "文章摘要...",
        "featured_image": "/uploads/images/cat1.jpg",
        "category": {
          "id": 1,
          "name": "猫咪护理"
        },
        "language": "zh",
        "status": "published",
        "created_at": "2024-01-01T00:00:00.000Z",
        "updated_at": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 10,
      "total_count": 100,
      "per_page": 10
    }
  }
}
```

### 3.2 获取文章详情
```
GET /articles/{id}
GET /articles/slug/{slug}?language=en
```

**响应数据**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "如何照顾小猫",
    "slug": "how-to-care-for-kittens",
    "content": "文章完整内容...",
    "excerpt": "文章摘要...",
    "featured_image": "/uploads/images/cat1.jpg",
    "category": {
      "id": 1,
      "name": "猫咪护理",
      "slug": "cat-care"
    },
    "language": "zh",
    "status": "published",
    "seo": {
      "meta_title": "SEO标题",
      "meta_description": "SEO描述",
      "meta_keywords": "关键词1,关键词2"
    },
    "translations": [
      {
        "language": "en",
        "title": "How to Care for Kittens",
        "slug": "how-to-care-for-kittens-en"
      }
    ],
    "created_at": "2024-01-01T00:00:00.000Z",
    "updated_at": "2024-01-01T00:00:00.000Z"
  }
}
```

### 3.3 创建文章
```
POST /admin/articles
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "title": "文章标题",
  "content": "文章内容",
  "excerpt": "文章摘要",
  "category_id": 1,
  "language": "zh",
  "status": "draft",
  "featured_image": "/uploads/images/cat1.jpg",
  "seo": {
    "meta_title": "SEO标题",
    "meta_description": "SEO描述",
    "meta_keywords": "关键词1,关键词2"
  }
}
```

### 3.4 更新文章
```
PUT /admin/articles/{id}
Authorization: Bearer {token}
```

### 3.5 删除文章
```
DELETE /admin/articles/{id}
Authorization: Bearer {token}
```

### 3.6 翻译文章
```
POST /admin/articles/{id}/translate
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "target_languages": ["en", "de", "ru"]
}
```

## 4. 分类管理API

### 4.1 获取分类列表
```
GET /categories?language=en&parent_id=0
```

**响应数据**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "猫咪护理",
      "slug": "cat-care",
      "description": "分类描述",
      "parent_id": 0,
      "language": "zh",
      "article_count": 15,
      "children": [
        {
          "id": 2,
          "name": "幼猫护理",
          "slug": "kitten-care",
          "parent_id": 1,
          "article_count": 5
        }
      ]
    }
  ]
}
```

### 4.2 创建分类
```
POST /admin/categories
Authorization: Bearer {token}
```

### 4.3 更新分类
```
PUT /admin/categories/{id}
Authorization: Bearer {token}
```

### 4.4 删除分类
```
DELETE /admin/categories/{id}
Authorization: Bearer {token}
```

## 5. 评论管理API

### 5.1 获取评论列表
```
GET /comments?article_id=1&status=approved&page=1&limit=20
```

**响应数据**:
```json
{
  "success": true,
  "data": {
    "comments": [
      {
        "id": 1,
        "content": "评论内容",
        "author_name": "用户名",
        "author_email": "<EMAIL>",
        "article_id": 1,
        "parent_id": 0,
        "status": "approved",
        "replies": [
          {
            "id": 2,
            "content": "回复内容",
            "parent_id": 1,
            "created_at": "2024-01-01T00:00:00.000Z"
          }
        ],
        "created_at": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_count": 50
    }
  }
}
```

### 5.2 提交评论
```
POST /comments
```

**请求参数**:
```json
{
  "article_id": 1,
  "parent_id": 0,
  "content": "评论内容",
  "author_name": "用户名",
  "author_email": "<EMAIL>"
}
```

### 5.3 审核评论
```
PUT /admin/comments/{id}/approve
PUT /admin/comments/{id}/reject
Authorization: Bearer {token}
```

## 6. 文件上传API

### 6.1 上传图片
```
POST /upload/image
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**请求参数**:
- `file`: 图片文件
- `type`: 上传类型(article/avatar/category)

**响应数据**:
```json
{
  "success": true,
  "data": {
    "url": "/uploads/images/2024/01/filename.jpg",
    "filename": "filename.jpg",
    "size": 102400,
    "mime_type": "image/jpeg"
  }
}
```

## 7. 搜索API

### 7.1 全站搜索
```
GET /search?q=关键词&language=en&page=1&limit=10
```

**响应数据**:
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "type": "article",
        "id": 1,
        "title": "文章标题",
        "excerpt": "匹配的内容片段...",
        "url": "/articles/how-to-care-for-kittens",
        "relevance": 0.95
      }
    ],
    "total_count": 25,
    "search_time": 0.05
  }
}
```

## 8. SEO相关API

### 8.1 生成网站地图
```
GET /sitemap.xml?language=en
```

### 8.2 获取robots.txt
```
GET /robots.txt
```

### 8.3 获取SEO配置
```
GET /seo/config?language=en
```

## 9. 系统配置API

### 9.1 获取系统配置
```
GET /admin/config
Authorization: Bearer {token}
```

### 9.2 更新系统配置
```
PUT /admin/config
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "site_settings": {
    "site_name": "宠物博客",
    "site_description": "专业的宠物知识分享平台"
  },
  "language_settings": {
    "default_language": "zh",
    "supported_languages": ["zh", "en", "de", "ru"]
  },
  "domain_mappings": {
    "petblog.com": "en",
    "haustierblog.de": "de",
    "питомцыблог.ru": "ru"
  },
  "ads_settings": {
    "google_adsense_enabled": true,
    "adsense_client_id": "ca-pub-xxxxxxxxxx"
  },
  "analytics_settings": {
    "google_analytics_enabled": true,
    "ga_tracking_id": "GA-XXXXXXXXX"
  }
}
```

## 10. 错误代码说明

| 错误代码 | HTTP状态码 | 说明 |
|---------|-----------|------|
| AUTH_REQUIRED | 401 | 需要认证 |
| AUTH_INVALID | 401 | 认证无效 |
| PERMISSION_DENIED | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| VALIDATION_ERROR | 422 | 参数验证失败 |
| SERVER_ERROR | 500 | 服务器内部错误 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 |

这个API设计文档为前后端开发提供了清晰的接口规范，确保开发过程中的一致性和可维护性。
