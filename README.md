# 宠物博客站群系统

一个专业的多语言宠物博客站群系统，专注于猫狗知识分享，采用前后端分离架构，严格遵循Google SEO最佳实践。

## 🚀 快速开始

### 环境要求
- Node.js 18.x LTS
- MySQL 5.7.44
- pnpm (推荐) 或 npm

### 安装依赖
```bash
# 克隆项目
git clone <repository-url>
cd pet-blog-system

# 安装后端依赖
cd backend
pnpm install

# 安装前端依赖  
cd ../frontend
pnpm install
```

### 环境配置
```bash
# 复制环境变量文件
cp backend/.env.example backend/.env

# 编辑 .env 文件配置数据库等
nano backend/.env
```

### 启动开发
```bash
# 启动后端API (端口3000)
cd backend
pnpm run dev

# 启动前端开发服务器 (端口4321)
cd frontend  
pnpm run dev
```

## 📖 功能说明

### ✅ 已实现功能
- **多语言支持**: 英语、德语、俄语三语言站点
- **前后端分离**: Astro前端 + Node.js后端
- **内容管理**: 富文本编辑器，支持图片上传
- **翻译工作流**: 中文原创→AI翻译→人工校对→发布
- **评论系统**: 多层嵌套评论，管理员审核
- **SEO优化**: 网站地图、结构化数据、多语言SEO
- **分类管理**: 两级分类体系
- **搜索功能**: 全站搜索支持

### 🚧 开发中功能
- **广告系统**: Google AdSense集成
- **统计分析**: Google Analytics集成
- **性能优化**: 缓存机制、CDN准备
- **管理后台**: 完整的内容管理界面

### 📋 计划功能
- **更多语言**: 法语、西班牙语、日语等
- **高级SEO**: 更多结构化数据类型
- **社交分享**: 社交媒体集成
- **邮件通知**: 评论通知系统

## 🛠️ 技术栈

### 前端
- **框架**: Astro 4.x
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **构建**: Vite

### 后端
- **运行时**: Node.js 18.x
- **框架**: Express.js
- **语言**: TypeScript
- **ORM**: Prisma
- **认证**: JWT

### 数据库
- **主数据库**: MySQL 5.7.44
- **缓存**: Redis (可选)

### 部署
- **服务器**: Linux VPS + 宝塔面板
- **Web服务器**: Nginx
- **进程管理**: PM2
- **SSL**: Let's Encrypt

## 🌍 多语言架构

### 域名映射
```
petblog.com      → 英语 (en)
haustierblog.de  → 德语 (de)  
питомцыблог.ru   → 俄语 (ru)
```

### 模板结构
```
frontend/src/templates/
├── en/          # 英语模板
├── de/          # 德语模板
└── ru/          # 俄语模板
```

### 内容工作流
```
中文原创 → AI翻译 → 人工校对 → 多语言发布
```

## 📊 API文档

### 基础信息
- **API版本**: v1
- **基础URL**: `/api/v1`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON

### 主要端点
```
GET    /api/v1/articles          # 获取文章列表
GET    /api/v1/articles/{id}     # 获取文章详情
POST   /api/v1/admin/articles    # 创建文章 (需认证)
PUT    /api/v1/admin/articles/{id} # 更新文章 (需认证)
DELETE /api/v1/admin/articles/{id} # 删除文章 (需认证)

GET    /api/v1/categories        # 获取分类列表
POST   /api/v1/comments          # 提交评论
GET    /api/v1/search            # 搜索文章
```

详细API文档请查看: [docs/03-API接口设计文档.md](docs/03-API接口设计文档.md)

## 🗄️ 数据库设计

### 核心表结构
- `users` - 用户管理
- `languages` - 语言配置  
- `domain_mappings` - 域名映射
- `categories` - 分类管理
- `articles` - 文章内容
- `comments` - 评论系统
- `uploads` - 文件管理

详细数据库设计请查看: [docs/04-数据库设计文档.md](docs/04-数据库设计文档.md)

## 🔧 开发指南

### 项目结构
```
pet-blog-system/
├── backend/           # Node.js后端
│   ├── src/
│   │   ├── routes/    # API路由
│   │   ├── models/    # 数据模型
│   │   ├── services/  # 业务逻辑
│   │   └── utils/     # 工具函数
│   └── tests/         # 后端测试
├── frontend/          # Astro前端
│   ├── src/
│   │   ├── templates/ # 多语言模板
│   │   ├── shared/    # 共享组件
│   │   └── config/    # 配置文件
│   └── tests/         # 前端测试
├── docs/              # 项目文档
├── database/          # 数据库脚本
└── scripts/           # 部署脚本
```

### 开发规范
- 使用TypeScript严格模式
- 遵循ESLint和Prettier规范
- 编写单元测试和集成测试
- 提交前运行完整测试套件

### 测试命令
```bash
# 后端测试
cd backend
pnpm test

# 前端测试
cd frontend  
pnpm test

# 端到端测试
pnpm test:e2e

# 测试覆盖率
pnpm test:coverage
```

## 📈 SEO优化

### 技术SEO
- ✅ 语义化HTML结构
- ✅ 自动生成XML网站地图
- ✅ 结构化数据标记 (JSON-LD)
- ✅ Open Graph和Twitter Card
- ✅ 多语言hreflang标签
- ✅ 移动端友好设计
- ✅ 页面加载速度优化

### 内容SEO
- ✅ 关键词优化
- ✅ 内部链接策略
- ✅ 图片alt标签优化
- ✅ 元标题和描述优化

详细SEO指南请查看: [docs/06-SEO优化实施指南.md](docs/06-SEO优化实施指南.md)

## 🚀 部署

### 生产环境部署
```bash
# 构建项目
pnpm run build

# 启动生产服务
pm2 start ecosystem.config.js

# 配置Nginx
sudo nginx -t && sudo nginx -s reload
```

### 环境配置
- **开发环境**: localhost
- **测试环境**: test.petblog.com  
- **生产环境**: 多域名部署

详细部署指南请查看: [docs/07-部署配置指南.md](docs/07-部署配置指南.md)

## 📋 开发进度

当前进度: **架构设计完成** ✅

详细开发计划请查看: [docs/02-详细开发进度表.md](docs/02-详细开发进度表.md)

## 📚 文档

- [项目架构设计文档](docs/01-项目架构设计文档.md)
- [详细开发进度表](docs/02-详细开发进度表.md)  
- [API接口设计文档](docs/03-API接口设计文档.md)
- [数据库设计文档](docs/04-数据库设计文档.md)
- [前端组件设计文档](docs/05-前端组件设计文档.md)
- [SEO优化实施指南](docs/06-SEO优化实施指南.md)
- [部署配置指南](docs/07-部署配置指南.md)
- [测试计划文档](docs/08-测试计划文档.md)
- [多语言模板扩展指南](docs/09-多语言模板扩展指南.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目维护者: [Your Name]
- 邮箱: [<EMAIL>]
- 项目链接: [https://github.com/yourusername/pet-blog-system](https://github.com/yourusername/pet-blog-system)

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
